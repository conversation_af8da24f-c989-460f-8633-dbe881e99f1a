<template>
  <div class="examine-statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <div class="icon-wrapper">
            <wd-icon name="chart-pie" size="24px" color="#4f46e5" />
          </div>
          <div class="title-text">
            <h1 class="page-title">核查统计表</h1>
            <p class="page-subtitle">Performance Statistics</p>
          </div>
        </div>

        <!-- 快速统计 -->
        <div class="quick-overview" v-if="Performance">
          <div class="overview-item">
            <div class="overview-value">{{ Performance.overallProgress?.totalPumpRooms || 0 }}</div>
            <div class="overview-label">总泵房数</div>
          </div>
          <div class="overview-item">
            <div class="overview-value">{{ Performance.overallProgress?.completedPumpRooms || 0 }}</div>
            <div class="overview-label">已完成</div>
          </div>
          <div class="overview-item">
            <div class="overview-value">{{ Performance.gridProgress?.length || 0 }}</div>
            <div class="overview-label">网格数量</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-container" v-if="!Performance">
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
      <div class="loading-text">正在加载数据...</div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content" v-else>
      <!-- 任务概览卡片 -->
      <div class="task-overview-card">
        <div class="card-header">
          <div class="header-left">
            <div class="header-icon">
              <wd-icon name="task" size="20px" color="#4f46e5" />
            </div>
            <div class="header-text">
              <h3 class="card-title">任务概览</h3>
              <p class="card-subtitle">Task Overview</p>
            </div>
          </div>
          <div class="status-badge" :class="getOverallProgressClass()">
            <span>{{ getOverallProgressText() }}</span>
          </div>
        </div>

        <div class="card-body">
          <!-- 任务基本信息 -->
          <div class="task-info-section">
            <div class="info-row">
              <div class="info-item" v-if="Performance.taskInfo?.TaskID">
                <div class="info-label">
                  <wd-icon name="edit" size="14px" color="#6b7280" />
                  <span>任务ID</span>
                </div>
                <div class="info-value">{{ Performance.taskInfo.TaskID }}</div>
              </div>

              <div class="info-item" v-if="Performance.taskInfo?.TaskEndTime">
                <div class="info-label">
                  <wd-icon name="time" size="14px" color="#6b7280" />
                  <span>截止时间</span>
                </div>
                <div class="info-value">{{ formatDateTime(Performance.taskInfo.TaskEndTime) }}</div>
              </div>
            </div>

            <div class="info-row" v-if="Performance.taskInfo?.TaskRemark">
              <div class="info-item full-width">
                <div class="info-label">
                  <wd-icon name="chat" size="14px" color="#6b7280" />
                  <span>任务描述</span>
                </div>
                <div class="info-value">{{ Performance.taskInfo.TaskRemark }}</div>
              </div>
            </div>
          </div>

          <!-- 总体进度 -->
          <div class="overall-progress-section" v-if="Performance.overallProgress">
            <div class="progress-header">
              <div class="progress-title">总体完成进度</div>
              <div class="progress-stats">
                <span class="progress-percentage">{{ getProgressPercentage(Performance.overallProgress) }}%</span>
                <span class="progress-detail"> {{ Performance.overallProgress.completedPumpRooms }}/{{ Performance.overallProgress.totalPumpRooms }} </span>
              </div>
            </div>

            <!-- 线性进度条 -->
            <div class="linear-progress-container">
              <div class="progress-track">
                <div class="progress-fill" :style="{ width: getProgressPercentage(Performance.overallProgress) + '%' }" :class="getProgressClass(Performance.overallProgress)">
                  <div class="progress-shine"></div>
                </div>
              </div>
            </div>
            <!-- 进度统计 -->
            <div class="progress-stats-grid">
              <div class="stat-item completed" @click="showPersonList('fulfill')">
                <div class="stat-icon">
                  <wd-icon name="check-circle" size="16px" color="#10b981" />
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ completeness?.fulfill?.length || 0 }}</div>
                  <div class="stat-label">已完成人</div>
                </div>
              </div>
              <div class="stat-item remaining" @click="showPersonList('underway')">
                <div class="stat-icon">
                  <wd-icon name="clock" size="16px" color="#f59e0b" />
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ completeness?.underway?.length || 0 }}</div>
                  <div class="stat-label">未完成人</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 网格进度统计 -->
      <div class="grid-progress-section" v-if="Performance.gridProgress && Performance.gridProgress.length > 0">
        <div class="section-header">
          <div class="header-left flex f-y-center">
            <div class="section-icon mar-R16">
              <wd-icon name="grid" size="20px" color="#10b981" />
            </div>
            <div class="section-text">
              <h3 class="section-title">网格进度统计</h3>
              <p class="section-subtitle">{{ Performance.gridProgress.length }} 个网格的详细进度</p>
            </div>
          </div>
        </div>

        <!-- 网格列表 -->
        <div class="grid-list">
          <div class="grid-item" v-for="(grid, index) in Performance.gridProgress" :key="index" @click="toggleGridDetail(index)">
            <!-- 网格基本信息 -->
            <div class="grid-header">
              <div class="grid-info">
                <div class="grid-name">
                  <div class="grid-icon">
                    <wd-icon name="location" size="16px" color="#4f46e5" />
                  </div>
                  <div class="grid-text">
                    <h4 class="grid-title">{{ grid.gridName }}</h4>
                    <p class="grid-stats">{{ grid.completedPumpRooms }}/{{ grid.totalPumpRooms }} 泵房</p>
                  </div>
                </div>

                <div class="grid-progress-summary">
                  <div class="progress-percentage">{{ getProgressPercentage(grid) }}%</div>
                  <div class="progress-status">{{ getGridProgressText(grid) }}</div>
                </div>
              </div>

              <div class="expand-icon">
                <wd-icon :name="expandedGrids.includes(index) ? 'chevron-up' : 'chevron-down'" size="18px" color="#9ca3af" />
              </div>
            </div>

            <!-- 网格进度条 -->
            <div class="grid-progress-bar">
              <div class="progress-track">
                <div class="progress-fill" :style="{ width: getProgressPercentage(grid) + '%' }" :class="getProgressClass(grid)">
                  <div class="progress-shine"></div>
                </div>
              </div>
            </div>

            <!-- 现代化网格详情（可展开） -->
            <div class="modern-grid-details" v-if="expandedGrids.includes(index)" :class="{ 'details-expanded': expandedGrids.includes(index) }">
              <!-- 负责人进度区域 -->
              <div class="modern-persons-section" v-if="grid.responsiblePersons">
                <div class="section-header">
                  <div class="section-icon">
                    <wd-icon name="user" size="16px" color="#8b5cf6" />
                  </div>
                  <h5 class="section-title">负责人进度</h5>
                  <div class="section-line"></div>
                </div>

                <!-- 主要负责人 -->
                <div class="person-category" v-if="grid.responsiblePersons.primary && grid.responsiblePersons.primary.length > 0">
                  <div class="category-header primary">
                    <div class="category-badge">
                      <wd-icon name="star" size="12px" color="#ffffff" />
                      <span>主要负责人</span>
                    </div>
                  </div>
                  <div class="person-cards">
                    <div class="person-card primary" v-for="(person, pIndex) in grid.responsiblePersons.primary" :key="`primary-${pIndex}`">
                      <div class="person-avatar">
                        <div class="avatar-bg primary">
                          <wd-icon name="user-circle" size="16px" color="#ffffff" />
                        </div>
                      </div>
                      <div class="person-details">
                        <div class="person-name">{{ person.name || person.username || `负责人 ${pIndex + 1}` }}</div>
                        <div class="person-station" v-if="person.station">{{ person.station }}</div>
                        <div class="person-stats">
                          <span class="stat-detail"> {{ person.completedPumpRooms || 0 }}/{{ person.totalPumpRooms || 0 }} </span>
                          <span class="stat-value">{{ getProgressPercentage(person) }}%</span>
                        </div>
                      </div>
                      <div class="person-progress-modern">
                        <div class="mini-progress-track">
                          <div class="mini-progress-fill primary" :style="{ width: getProgressPercentage(person) + '%' }">
                            <div class="mini-progress-glow"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 次要负责人 -->
                <div class="person-category" v-if="grid.responsiblePersons.secondary && grid.responsiblePersons.secondary.length > 0">
                  <div class="category-header secondary">
                    <div class="category-badge secondary">
                      <wd-icon name="user" size="12px" color="#ffffff" />
                      <span>次要负责人</span>
                    </div>
                  </div>
                  <div class="person-cards">
                    <div class="person-card secondary" v-for="(person, pIndex) in grid.responsiblePersons.secondary" :key="`secondary-${pIndex}`">
                      <div class="person-avatar">
                        <div class="avatar-bg secondary">
                          <wd-icon name="user-circle" size="16px" color="#ffffff" />
                        </div>
                      </div>
                      <div class="person-details">
                        <div class="person-name">{{ person.name || person.username || `负责人 ${pIndex + 1}` }}</div>
                        <div class="person-station" v-if="person.station">{{ person.station }}</div>
                        <div class="person-stats">
                          <span class="stat-value">{{ getProgressPercentage(person) }}%</span>
                          <span class="stat-detail"> {{ person.completedPumpRooms || 0 }}/{{ person.totalPumpRooms || 0 }} </span>
                        </div>
                      </div>
                      <div class="person-progress-modern">
                        <div class="mini-progress-track">
                          <div class="mini-progress-fill secondary" :style="{ width: getProgressPercentage(person) + '%' }">
                            <div class="mini-progress-glow"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 现代化空状态 -->
  <div class="modern-empty-state" v-if="!Performance?.gridProgress || Performance.gridProgress.length === 0">
    <div class="empty-animation">
      <div class="empty-circle"></div>
      <div class="empty-circle"></div>
      <div class="empty-circle"></div>
    </div>
    <div class="empty-content">
      <div class="empty-icon">
        <wd-icon name="chart-pie" size="48px" color="#d1d5db" />
      </div>
      <h3 class="empty-title">暂无网格数据</h3>
      <p class="empty-description">当前任务暂无网格进度统计信息</p>
    </div>
  </div>

  <!-- 人员列表弹窗 -->
  <wd-popup v-model="showPersonModal" closable position="bottom" custom-style="min-height: 80%;" :safe-area-inset-bottom="true">
    <div class="person-modal">
      <div class="modal-header">
        <h3 class="modal-title">{{ personModalTitle }}</h3>
        <div class="modal-count">共 {{ personModalList.length }} 人</div>
      </div>
      <div class="modal-content">
        <div v-if="personModalList.length === 0" class="empty-state">
          <wd-icon name="user" size="32px" color="#d1d5db" />
          <p>暂无人员数据</p>
        </div>
        <div v-else class="person-list">
          <div v-for="(person, index) in personModalList" :key="person.userId || index" class="person-item">
            <div class="person-info">
              <div class="person-avatar">
                <div class="avatar-bg">
                  <wd-icon name="user" size="16px" color="#ffffff" />
                </div>
              </div>
              <div class="person-details">
                <div class="person-name">{{ person.name || person.username || `用户 ${person.userId}` }}</div>
                <div class="person-station" v-if="person.station">{{ person.station }}</div>
              </div>
            </div>
            <div class="person-progress">
              <div class="progress-info">
                <span class="progress-text">{{ person.completedPumpRooms || 0 }}/{{ person.totalPumpRooms || 0 }}</span>
                <span class="progress-percent">{{ person.progressPercentage || 0 }}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: `${person.progressPercentage || 0}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </wd-popup>

  <wd-toast />
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { PumpHouseApi } from '@/services/model/pump.house'
import { useToast } from 'wot-design-uni'
import moment from 'moment'

// 设置 moment 中文本地化
moment.locale('zh-cn')

const toast = useToast()

onLoad(({ id }) => getPerformance(id))

const Performance = ref(null)
const expandedGrids = ref([]) // 展开的网格索引数组

// 弹窗相关
const showPersonModal = ref(false)
const personModalTitle = ref('')
const personModalList = ref([])

// 获取性能数据
async function getPerformance(id) {
  toast.loading('正在加载...')
  try {
    const { data } = await PumpHouseApi.performance(id)
    Performance.value = data
    console.log('Performance数据1212:', data)
    toast.close()
  } catch (error) {
    console.error('数据加载失败:', error)
    toast.close()
    toast.error('数据加载失败')
  }
}

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '--'
  try {
    const momentDate = moment(dateTime)
    if (!momentDate.isValid()) return dateTime
    return momentDate.format('YYYY-MM-DD HH:mm')
  } catch (error) {
    console.warn('formatDateTime error:', error)
    return dateTime
  }
}

// 获取进度百分比
function getProgressPercentage(progressData) {
  if (!progressData) return 0

  // 优先使用已计算的progressPercentage字段
  if (progressData.progressPercentage !== undefined) {
    return Math.round(progressData.progressPercentage)
  }

  // 如果直接有percentage字段
  if (progressData.percentage !== undefined) {
    return Math.round(progressData.percentage)
  }

  // 如果有completedPumpRooms和totalPumpRooms字段（适用于overallProgress、gridProgress和responsiblePersons）
  if (progressData.completedPumpRooms !== undefined && progressData.totalPumpRooms !== undefined) {
    const total = parseInt(progressData.totalPumpRooms) || 0
    const completed = parseInt(progressData.completedPumpRooms) || 0
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  // 如果有completed和total字段（兼容性）
  if (progressData.completed !== undefined && progressData.total !== undefined) {
    const total = parseInt(progressData.total) || 0
    const completed = parseInt(progressData.completed) || 0
    return total > 0 ? Math.round((completed / total) * 100) : 0
  }

  return 0
}

// 获取总体进度状态类（基于任务截止时间）
function getOverallProgressClass() {
  if (!Performance.value?.taskInfo) return 'status-unknown'

  const endTime = Performance.value.taskInfo.TaskEndTime
  if (!endTime) return 'status-in-progress'

  const now = moment()
  const taskEndTime = moment(endTime)

  if (!taskEndTime.isValid()) return 'status-in-progress'

  if (now.isAfter(taskEndTime)) {
    return 'status-ended'
  } else {
    return 'status-in-progress'
  }
}

// 获取总体进度文本（基于任务截止时间）
function getOverallProgressText() {
  if (!Performance.value?.taskInfo) return '未知状态'

  const endTime = Performance.value.taskInfo.TaskEndTime
  if (!endTime) return '进行中'

  const now = moment()
  const taskEndTime = moment(endTime)

  if (!taskEndTime.isValid()) return '进行中'

  if (now.isAfter(taskEndTime)) {
    return '已结束'
  } else {
    return '进行中'
  }
}

// 获取进度条样式类
function getProgressClass(progressData) {
  const percentage = getProgressPercentage(progressData)
  if (percentage >= 100) return 'progress-completed'
  if (percentage >= 80) return 'progress-nearly-done'
  if (percentage >= 50) return 'progress-in-progress'
  if (percentage > 0) return 'progress-started'
  return 'progress-not-started'
}

// 获取网格进度文本
function getGridProgressText(grid) {
  if (!grid) return '无数据'

  const percentage = getProgressPercentage(grid)
  if (percentage >= 100) return '已完成'
  if (percentage >= 80) return '接近完成'
  if (percentage >= 50) return '进行中'
  if (percentage > 0) return '已开始'
  return '未开始'
}

// 切换网格详情展开状态
function toggleGridDetail(index) {
  const currentIndex = expandedGrids.value.indexOf(index)
  if (currentIndex > -1) {
    expandedGrids.value.splice(currentIndex, 1)
  } else {
    expandedGrids.value.push(index)
  }
}

// 显示人员列表弹窗
function showPersonList(type) {
  if (!completeness.value) return

  const data = completeness.value[type]
  if (!data || !data.list) return

  personModalTitle.value = type === 'fulfill' ? '已完成人员' : '未完成人员'
  personModalList.value = data.list
  showPersonModal.value = true
}

const completeness = computed(() => {
  const list = Performance.value?.gridProgress.map((item) => item.responsiblePersons.primary).flat() ?? []

  // 已经完成的人员
  const fulfillValue = list.filter((item) => item.totalPumpRooms && item.totalPumpRooms === item.completedPumpRooms).sort((a, b) => b.progressPercentage - a.progressPercentage)
  // 进行中
  const underwayValue = list.filter((item) => !item.totalPumpRooms || item.totalPumpRooms !== item.completedPumpRooms).sort((a, b) => b.progressPercentage - a.progressPercentage)
  console.log('flatList', {
    fulfill: { length: fulfillValue.length, list: fulfillValue },
    underway: { length: underwayValue.length, list: underwayValue }
  })
  return {
    fulfill: { length: fulfillValue.length, list: fulfillValue },
    underway: { length: underwayValue.length, list: underwayValue }
  }
})
</script>

<style lang="less" scoped>
.examine-statistics {
  min-height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  // 页面头部
  .page-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    padding: 60rpx 32rpx 40rpx;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .header-content {
      position: relative;
      z-index: 2;

      .title-section {
        display: flex;
        align-items: center;
        gap: 20rpx;
        margin-bottom: 40rpx;

        .icon-wrapper {
          width: 60rpx;
          height: 60rpx;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .title-text {
          flex: 1;

          .page-title {
            font-size: 40rpx;
            font-weight: 700;
            margin: 0 0 8rpx 0;
            color: #ffffff;
          }

          .page-subtitle {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1rpx;
          }
        }
      }

      .quick-overview {
        display: flex;
        gap: 32rpx;
        justify-content: center;

        .overview-item {
          text-align: center;
          padding: 24rpx;
          background: rgba(255, 255, 255, 0.15);
          border-radius: 16rpx;

          border: 1rpx solid rgba(255, 255, 255, 0.2);
          flex: 1;

          .overview-value {
            font-size: 32rpx;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8rpx;
          }

          .overview-label {
            font-size: 22rpx;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
  }

  // 加载状态
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;
    text-align: center;

    .loading-spinner {
      margin-bottom: 32rpx;

      .spinner {
        width: 40rpx;
        height: 40rpx;
        border: 4rpx solid #e5e7eb;
        border-top: 4rpx solid #4f46e5;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    .loading-text {
      font-size: 28rpx;
      color: #6b7280;
      font-weight: 500;
    }
  }

  // 主要内容
  .main-content {
    padding: 32rpx;
    max-width: 1200rpx;
    margin: 0 auto;
    z-index: 2;
  }

  // 任务概览卡片
  .task-overview-card {
    background: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
    margin-bottom: 32rpx;
    overflow: hidden;
    border: 1rpx solid #e5e7eb;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      border-bottom: 1rpx solid #f3f4f6;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .header-icon {
          width: 48rpx;
          height: 48rpx;
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .header-text {
          .card-title {
            font-size: 28rpx;
            font-weight: 700;
            color: #1f2937;
            margin: 0 0 4rpx 0;
          }

          .card-subtitle {
            font-size: 22rpx;
            color: #6b7280;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 1rpx;
          }
        }
      }

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 22rpx;
        font-weight: 600;

        &.status-in-progress {
          background: #dbeafe;
          color: #2563eb;
        }

        &.status-ended {
          background: #f3f4f6;
          color: #6b7280;
        }
      }
    }

    .card-body {
      padding: 32rpx;

      // 任务信息部分
      .task-info-section {
        margin-bottom: 32rpx;

        .info-row {
          display: flex;
          gap: 24rpx;
          margin-bottom: 20rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .info-item {
            flex: 1;

            &.full-width {
              flex: none;
              width: 100%;
            }

            .info-label {
              display: flex;
              align-items: center;
              gap: 8rpx;
              font-size: 22rpx;
              color: #6b7280;
              margin-bottom: 8rpx;
              font-weight: 500;
            }

            .info-value {
              font-size: 24rpx;
              color: #1f2937;
              font-weight: 600;
              line-height: 140%;
            }
          }
        }
      }

      // 总体进度部分
      .overall-progress-section {
        .progress-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20rpx;

          .progress-title {
            font-size: 26rpx;
            font-weight: 700;
            color: #1f2937;
          }

          .progress-stats {
            display: flex;
            align-items: center;
            gap: 12rpx;

            .progress-percentage {
              font-size: 28rpx;
              font-weight: 700;
              color: #4f46e5;
            }

            .progress-detail {
              font-size: 22rpx;
              color: #6b7280;
            }
          }
        }

        .linear-progress-container {
          margin-bottom: 24rpx;

          .progress-track {
            height: 12rpx;
            background: #f3f4f6;
            border-radius: 6rpx;
            overflow: hidden;
            position: relative;

            .progress-fill {
              height: 100%;
              border-radius: 6rpx;
              position: relative;
              transition: width 0.8s ease;
              overflow: hidden;

              .progress-shine {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
                animation: shine 2s infinite;
              }

              &.progress-completed {
                background: linear-gradient(90deg, #10b981, #059669);
              }

              &.progress-nearly-done {
                background: linear-gradient(90deg, #f59e0b, #d97706);
              }

              &.progress-in-progress {
                background: linear-gradient(90deg, #4f46e5, #3730a3);
              }

              &.progress-started {
                background: linear-gradient(90deg, #7c3aed, #5b21b6);
              }

              &.progress-not-started {
                background: linear-gradient(90deg, #ef4444, #dc2626);
              }
            }
          }
        }

        .progress-stats-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16rpx;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 12rpx;
            padding: 20rpx;
            background: #f9fafb;
            border-radius: 12rpx;
            border: 1rpx solid #e5e7eb;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              right: 16rpx;
              transform: translateY(-50%);
              width: 12rpx;
              height: 12rpx;
              border-top: 2rpx solid #9ca3af;
              border-right: 2rpx solid #9ca3af;
              transform: translateY(-50%) rotate(45deg);
            }

            &:active {
              background: #f3f4f6;
              border-color: #d1d5db;
            }

            .stat-content {
              flex: 1;

              .stat-number {
                font-size: 24rpx;
                font-weight: 700;
                color: #1f2937;
                margin-bottom: 2rpx;
              }

              .stat-label {
                font-size: 20rpx;
                color: #6b7280;
              }
            }
          }
        }
      }
    }
  }
  // 网格进度部分
  .grid-progress-section {
    margin-top: 32rpx;

    .section-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 24rpx;
      padding: 0 8rpx;

      .section-icon {
        width: 40rpx;
        height: 40rpx;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .section-text {
        flex: 1;

        .section-title {
          font-size: 28rpx;
          font-weight: 700;
          color: #1f2937;
          margin: 0 0 4rpx 0;
        }

        .section-subtitle {
          font-size: 22rpx;
          color: #6b7280;
          margin: 0;
        }
      }
    }

    .grid-list {
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      .grid-item {
        background: #ffffff;
        border-radius: 12rpx;
        border: 1rpx solid #e5e7eb;
        overflow: hidden;

        .grid-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 24rpx;

          .grid-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;

            .grid-name {
              display: flex;
              align-items: center;
              gap: 12rpx;

              .grid-icon {
                width: 32rpx;
                height: 32rpx;
                background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
                border-radius: 8rpx;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .grid-text {
                .grid-title {
                  font-size: 26rpx;
                  font-weight: 700;
                  color: #1f2937;
                  margin: 0 0 4rpx 0;
                }

                .grid-stats {
                  font-size: 22rpx;
                  color: #6b7280;
                  margin: 0;
                }
              }
            }

            .grid-progress-summary {
              text-align: right;

              .progress-percentage {
                font-size: 28rpx;
                font-weight: 700;
                color: #4f46e5;
                margin-bottom: 4rpx;
              }

              .progress-status {
                font-size: 20rpx;
                color: #6b7280;
              }
            }
          }

          .expand-icon {
            margin-left: 16rpx;
            transition: transform 0.3s ease;
          }
        }

        .grid-progress-bar {
          padding: 0 24rpx 24rpx;

          .progress-track {
            height: 8rpx;
            background: #f3f4f6;
            border-radius: 4rpx;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              border-radius: 4rpx;
              position: relative;
              transition: width 0.6s ease;

              .progress-shine {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
                animation: shine 2s infinite;
              }

              &.progress-completed {
                background: linear-gradient(90deg, #10b981, #059669);
              }

              &.progress-nearly-done {
                background: linear-gradient(90deg, #f59e0b, #d97706);
              }

              &.progress-in-progress {
                background: linear-gradient(90deg, #4f46e5, #3730a3);
              }

              &.progress-started {
                background: linear-gradient(90deg, #7c3aed, #5b21b6);
              }

              &.progress-not-started {
                background: linear-gradient(90deg, #ef4444, #dc2626);
              }
            }
          }
        }
      }
      &.status-nearly-done {
        background: rgba(255, 193, 7, 0.9);
      }
      &.status-in-progress {
        background: rgba(0, 123, 255, 0.9);
      }
      &.status-started {
        background: rgba(108, 117, 125, 0.9);
      }
      &.status-not-started {
        background: rgba(220, 53, 69, 0.9);
      }
    }
  }

  .card-content {
    padding: 32rpx;
  }
}

// 任务信息部分
.task-info-section {
  margin-bottom: 40rpx;

  .info-grid {
    display: grid;
    gap: 24rpx;

    .info-item {
      .info-label {
        display: flex;
        align-items: center;
        gap: 12rpx;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .info-value {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        line-height: 140%;
      }
    }
  }
}

// 总体进度部分
.overall-progress-section {
  .progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .progress-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #2c3e50;
    }

    .progress-percentage {
      font-size: 32rpx;
      font-weight: 700;
      color: #4a90e2;
    }
  }

  .progress-bar-container {
    margin-bottom: 24rpx;
  }

  .progress-details {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;

    .detail-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8rpx;

      .detail-label {
        font-size: 24rpx;
        color: #7f8c8d;
      }

      .detail-value {
        font-size: 28rpx;
        font-weight: 600;

        &.completed {
          color: #27ae60;
        }

        &.total {
          color: #3498db;
        }
      }
    }

    .detail-divider {
      font-size: 32rpx;
      color: #bdc3c7;
      font-weight: 300;
    }
  }
}

// 网格进度部分
.grid-progress-card {
  .grid-list {
    .grid-item {
      border: 2rpx solid #f0f0f0;
      border-radius: 16rpx;
      margin-bottom: 24rpx;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      .grid-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx;
        background: #fafbfc;

        .grid-info {
          flex: 1;

          .grid-name {
            display: flex;
            align-items: center;
            gap: 12rpx;
            font-size: 28rpx;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8rpx;
          }

          .grid-progress-summary {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .progress-text {
              font-size: 24rpx;
              color: #7f8c8d;
            }

            .progress-percent {
              font-size: 26rpx;
              font-weight: 600;
              color: #4a90e2;
            }
          }
        }

        .expand-icon {
          padding: 8rpx;
        }
      }

      .grid-progress-bar {
        padding: 0 24rpx 24rpx;
      }

      .grid-details {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;

        &.details-expanded {
          max-height: 2000rpx;
        }

        .responsible-persons-section {
          padding: 24rpx;
          border-top: 2rpx solid #f0f0f0;
          background: #f8f9fa;

          .section-title {
            display: flex;
            align-items: center;
            gap: 12rpx;
            font-size: 26rpx;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20rpx;
          }

          .person-group {
            margin-bottom: 24rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .group-title {
              font-size: 24rpx;
              color: #7f8c8d;
              margin-bottom: 16rpx;
              font-weight: 500;
            }

            .person-list {
              .person-item {
                background: white;
                border-radius: 12rpx;
                padding: 20rpx;
                margin-bottom: 16rpx;
                border: 2rpx solid #f0f0f0;

                &:last-child {
                  margin-bottom: 0;
                }

                &.primary {
                  border-left: 6rpx solid #ff6b6b;
                }

                &.secondary {
                  border-left: 6rpx solid #52c41a;
                }

                .person-info {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 12rpx;

                  .person-name {
                    display: flex;
                    align-items: center;
                    gap: 8rpx;
                    font-size: 26rpx;
                    font-weight: 500;
                    color: #2c3e50;
                  }

                  .person-progress {
                    display: flex;
                    align-items: center;
                    gap: 8rpx;

                    .progress-text {
                      font-size: 24rpx;
                      font-weight: 600;
                      color: #4a90e2;
                    }

                    .progress-detail {
                      font-size: 22rpx;
                      color: #7f8c8d;
                    }
                  }
                }

                .person-progress-bar {
                  .progress-bar.small {
                    height: 8rpx;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);

  .empty-icon {
    margin-bottom: 24rpx;
  }

  .empty-text {
    font-size: 32rpx;
    color: #2c3e50;
    margin-bottom: 12rpx;
    font-weight: 600;
  }

  .empty-hint {
    font-size: 26rpx;
    color: #7f8c8d;
    line-height: 140%;
  }
}

// 进度条通用样式
.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  position: relative;

  &.small {
    height: 8rpx;
    border-radius: 4rpx;
  }

  .progress-fill {
    height: 100%;
    border-radius: inherit;
    transition: width 0.6s ease;
    position: relative;

    // 进度条颜色
    &.progress-completed,
    &.primary {
      background: linear-gradient(90deg, #27ae60, #2ecc71);
    }

    &.progress-nearly-done {
      background: linear-gradient(90deg, #f39c12, #e67e22);
    }

    &.progress-in-progress,
    &.secondary {
      background: linear-gradient(90deg, #3498db, #2980b9);
    }

    &.progress-started {
      background: linear-gradient(90deg, #95a5a6, #7f8c8d);
    }

    &.progress-not-started {
      background: linear-gradient(90deg, #e74c3c, #c0392b);
    }

    // 进度条动画效果
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      animation: shimmer 2s infinite;
    }
  }
}

// 动画定义
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 进度可视化区域
.progress-visualization {
  margin-bottom: 40rpx;

  .progress-circle-container {
    display: flex;
    justify-content: center;
    margin-bottom: 40rpx;

    .progress-circle {
      position: relative;
      width: 240rpx;
      height: 240rpx;

      .progress-svg {
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);
      }

      .progress-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;

        .progress-value {
          font-size: 48rpx;
          font-weight: 700;
          color: #ffffff;
          line-height: 1;
          margin-bottom: 8rpx;
        }

        .progress-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;

    .stat-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 20rpx;
      padding: 24rpx;
      text-align: center;

      border: 1rpx solid rgba(255, 255, 255, 0.1);

      .stat-icon {
        margin-bottom: 16rpx;
      }

      .stat-content {
        .stat-number {
          font-size: 32rpx;
          font-weight: 700;
          color: #ffffff;
          margin-bottom: 8rpx;
        }

        .stat-text {
          font-size: 22rpx;
          color: rgba(255, 255, 255, 0.7);
        }
      }

      &.completed {
        border-left: 4rpx solid #10b981;
      }

      &.total {
        border-left: 4rpx solid #6366f1;
      }

      &.remaining {
        border-left: 4rpx solid #f59e0b;
      }
    }
  }
}

// 任务详细信息网格
.task-details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;

  .detail-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20rpx;
    padding: 24rpx;

    border: 1rpx solid rgba(255, 255, 255, 0.1);

    &.full-width {
      grid-column: 1 / -1;
    }

    .detail-icon {
      margin-bottom: 16rpx;
    }

    .detail-content {
      .detail-label {
        font-size: 22rpx;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 8rpx;
      }

      .detail-value {
        font-size: 26rpx;
        font-weight: 600;
        color: #ffffff;
        line-height: 140%;
      }
    }
  }
}

// 现代化网格列表
.modern-grid-list {
  .grid-item-modern {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .grid-card {
      position: relative;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 24rpx;
      overflow: hidden;

      border: 1rpx solid rgba(255, 255, 255, 0.1);
    }
  }
}

// 网格头部现代化
.grid-header-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;

  .grid-info-section {
    display: flex;
    align-items: center;
    flex: 1;

    .grid-name-modern {
      display: flex;
      align-items: center;
      gap: 20rpx;
      flex: 1;

      .grid-icon {
        width: 48rpx;
        height: 48rpx;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
      }

      .grid-text {
        .grid-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #ffffff;
          margin: 0 0 4rpx 0;
        }

        .grid-subtitle {
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.6);
          margin: 0;
          text-transform: uppercase;
          letter-spacing: 1rpx;
        }
      }
    }

    .grid-progress-info {
      text-align: right;
      margin-left: 24rpx;

      .progress-percentage-large {
        font-size: 36rpx;
        font-weight: 700;
        color: #ffffff;
        line-height: 1;
      }

      .progress-status-text {
        font-size: 20rpx;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 4rpx;
      }
    }
  }

  .expand-control {
    margin-left: 24rpx;

    .expand-button {
      width: 48rpx;
      height: 48rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &.expanded {
        background: rgba(99, 102, 241, 0.3);
      }
    }
  }
}

// 现代化进度条区域
.modern-progress-section {
  padding: 0 32rpx 32rpx;

  .progress-track {
    height: 12rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6rpx;
    overflow: hidden;
    position: relative;
    margin-bottom: 16rpx;

    .progress-fill-modern {
      height: 100%;
      border-radius: 6rpx;
      position: relative;
      transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);

      .progress-shine {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shine 2s infinite;
      }

      &.modern-completed {
        background: linear-gradient(90deg, #10b981, #059669);
      }

      &.modern-nearly-done {
        background: linear-gradient(90deg, #f59e0b, #d97706);
      }

      &.modern-in-progress {
        background: linear-gradient(90deg, #6366f1, #4f46e5);
      }

      &.modern-started {
        background: linear-gradient(90deg, #8b5cf6, #7c3aed);
      }

      &.modern-not-started {
        background: linear-gradient(90deg, #ef4444, #dc2626);
      }
    }
  }

  .progress-markers {
    display: flex;
    justify-content: space-between;
    padding: 0 8rpx;

    .marker {
      font-size: 18rpx;
      color: rgba(255, 255, 255, 0.4);
      transition: color 0.3s ease;

      &.active {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 600;
      }
    }
  }
}

// 现代化网格详情
.modern-grid-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  &.details-expanded {
    max-height: 2000rpx;
  }

  .modern-persons-section {
    padding: 32rpx;
    background: rgba(139, 92, 246, 0.08);
    border-radius: 16rpx;
    border: 1rpx solid rgba(139, 92, 246, 0.15);

    .section-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 32rpx;

      .section-icon {
        width: 40rpx;
        height: 40rpx;
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(139, 92, 246, 0.25);
      }

      .section-title {
        font-size: 26rpx;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }

      .section-line {
        flex: 1;
        height: 2rpx;
        background: linear-gradient(90deg, rgba(139, 92, 246, 0.3), transparent);
      }
    }
  }
}

// 负责人分类
.person-category {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .category-header {
    margin-bottom: 20rpx;

    .category-badge {
      display: inline-flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-size: 22rpx;
      font-weight: 600;
      color: #ffffff;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);

      &.primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
      }

      &.secondary {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }
    }
  }

  .person-cards {
    display: grid;
    gap: 16rpx;

    .person-card {
      display: flex;
      align-items: center;
      gap: 16rpx;
      padding: 16rpx;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 12rpx;
      border: 1rpx solid rgba(255, 255, 255, 0.2);
      margin-bottom: 12rpx;
      backdrop-filter: blur(10rpx);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

      &.primary {
        border-left: 4rpx solid #3b82f6;
        background: rgba(59, 130, 246, 0.08);
        border-color: rgba(59, 130, 246, 0.2);
      }

      &.secondary {
        border-left: 4rpx solid #10b981;
        background: rgba(16, 185, 129, 0.08);
        border-color: rgba(16, 185, 129, 0.2);
      }

      .person-avatar {
        .avatar-bg {
          width: 48rpx;
          height: 48rpx;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          &.primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
          }

          &.secondary {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          }
        }
      }

      .person-details {
        flex: 1;

        .person-name {
          font-size: 26rpx;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.95);
          margin-bottom: 4rpx;
        }

        .person-station {
          font-size: 20rpx;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 8rpx;
        }

        .person-stats {
          display: flex;
          align-items: center;
          gap: 12rpx;

          .stat-value {
            font-size: 24rpx;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.9);
          }

          .stat-detail {
            font-size: 20rpx;
            color: rgba(255, 255, 255, 0.6);
          }
        }
      }

      .person-progress-modern {
        width: 120rpx;

        .mini-progress-track {
          height: 6rpx;
          background: #f3f4f6;
          border-radius: 3rpx;
          overflow: hidden;

          .mini-progress-fill {
            height: 100%;
            border-radius: 3rpx;
            transition: width 0.6s ease;

            &.primary {
              background: linear-gradient(90deg, #3b82f6, #2563eb);
            }

            &.secondary {
              background: linear-gradient(90deg, #10b981, #059669);
            }
          }
        }
      }
    }
  }
}

// 现代化空状态
.modern-empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 32rpx;

  border: 1rpx solid rgba(255, 255, 255, 0.1);

  .empty-animation {
    display: flex;
    justify-content: center;
    gap: 12rpx;
    margin-bottom: 40rpx;

    .empty-circle {
      width: 12rpx;
      height: 12rpx;
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      border-radius: 50%;
      animation: wave 1.5s ease-in-out infinite;

      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  .empty-content {
    .empty-icon {
      margin-bottom: 24rpx;
      opacity: 0.6;
    }

    .empty-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #ffffff;
      margin: 0 0 16rpx 0;
    }

    .empty-description {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.7);
      line-height: 150%;
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .modern-header {
    padding: 40rpx 24rpx 60rpx;

    .header-content .header-main {
      .title-section {
        .title-text .main-title {
          font-size: 40rpx;
        }
      }

      .quick-stats {
        flex-direction: column;
        gap: 20rpx;

        .stat-divider {
          width: 60rpx;
          height: 2rpx;
        }
      }
    }
  }

  .content-container {
    padding: 0 24rpx 60rpx;
  }

  .progress-visualization .stats-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .task-details-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }

  .person-cards .person-card {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;

    .person-progress-modern {
      width: 100%;
    }
  }

  // 动画定义
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes shine {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  // 响应式设计
  @media (max-width: 750rpx) {
    .page-header {
      padding: 40rpx 24rpx 32rpx;

      .header-content {
        .title-section {
          .title-text {
            .page-title {
              font-size: 36rpx;
            }
          }
        }

        .quick-overview {
          flex-direction: column;
          gap: 16rpx;

          .overview-item {
            flex: none;
          }
        }
      }
    }

    .main-content {
      padding: 24rpx;
    }

    .task-overview-card {
      .card-body {
        .task-info-section {
          .info-row {
            flex-direction: column;
            gap: 16rpx;
          }
        }

        .overall-progress-section {
          .progress-stats-grid {
            grid-template-columns: 1fr;
            gap: 12rpx;
          }
        }
      }
    }

    .grid-progress-section {
      .grid-list {
        .grid-item {
          .grid-header {
            .grid-info {
              flex-direction: column;
              align-items: flex-start;
              gap: 12rpx;

              .grid-progress-summary {
                text-align: left;
              }
            }
          }
        }
      }
    }
  }
}

// 人员列表弹窗样式
.person-modal {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;

  .modal-header {
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid #f3f4f6;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .modal-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
    }

    .modal-count {
      font-size: 24rpx;
      color: #6b7280;
      background: #f3f4f6;
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
    }
  }

  .modal-content {
    max-height: 80vh;
    overflow-y: auto;

    .empty-state {
      padding: 80rpx 32rpx;
      text-align: center;
      color: #9ca3af;

      p {
        margin: 16rpx 0 0;
        font-size: 28rpx;
      }
    }

    .person-list {
      padding: 16rpx 0;

      .person-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f9fafb;

        &:last-child {
          border-bottom: none;
        }

        .person-info {
          display: flex;
          align-items: center;
          gap: 16rpx;
          flex: 1;

          .person-avatar {
            .avatar-bg {
              width: 48rpx;
              height: 48rpx;
              border-radius: 12rpx;
              background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .person-details {
            .person-name {
              font-size: 28rpx;
              font-weight: 500;
              color: #1f2937;
              margin-bottom: 4rpx;
            }

            .person-station {
              font-size: 22rpx;
              color: #6b7280;
            }
          }
        }

        .person-progress {
          text-align: right;
          min-width: 120rpx;

          .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8rpx;
            gap: 16rpx;

            .progress-text {
              font-size: 22rpx;
              color: #6b7280;
            }

            .progress-percent {
              font-size: 24rpx;
              font-weight: 600;
              color: #3b82f6;
            }
          }

          .progress-bar {
            width: 100%;
            height: 6rpx;
            background: #f3f4f6;
            border-radius: 3rpx;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              background: linear-gradient(90deg, #3b82f6, #2563eb);
              border-radius: 3rpx;
              transition: width 0.6s ease;
            }
          }
        }
      }
    }
  }
}
</style>
